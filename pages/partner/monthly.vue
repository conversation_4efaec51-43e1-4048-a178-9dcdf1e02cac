<template>
  <view>

  </view>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerMonthlyCommissionListData} from "@/common/api/partner"

interface Item{
  yearMonth: string; // 年月 yyyy-MM
  amount: number;
  status: number; // 状态:1=待入账,2=已入账,3=已拒绝
  createTime: string;
}

const list = ref<Item[]>([])

const init = async () => {
  try {
    list.value = await getPartnerMonthlyCommissionListData()
  } catch (error) {
    console.error('获取月度奖励列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

onShow(() => {
  init()
})

</script>


<style scoped lang="scss">

</style>
